<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="FF5722">#674FF7</color>
    <color name="bg">#E8EAEC</color>
    <color name="black">#292929</color>
    <color name="blue">#0077F6</color>
    <color name="colorAccent">#0077F6</color>
    <color name="colorPrimary">#0077F6</color>
    <color name="colorPrimaryDark">#0077F6</color>
    <color name="color_connect">#0077F6</color>
    <color name="color_connecting">#3F85FF</color>
    <color name="color_disconnect">#FF5157</color>
    <color name="gray">#919191</color>
    <color name="green">#067E66</color>
    <color name="green_dark">#014B55</color>
    <color name="green_lite">#2196F3</color>
    <color name="ic_launcher_background">#CFDDEA</color>
    <color name="icon">#7888a2</color>
    <color name="orange">#D86005</color>
    <color name="red">#FF5157</color>
    <color name="statuscolor">#FCFCFE</color>
    <color name="text">#4f5c72</color>
    <color name="white">#ffffff</color>
    <plurals name="days_left">
        <item quantity="one">One day left</item>
        <item quantity="other">%d days left</item>
    </plurals>
    <plurals name="hours_left">
        <item quantity="one">One hour left</item>
        <item quantity="other">%d hours left</item>
    </plurals>
    <plurals name="months_left">
        <item quantity="one">One month left</item>
        <item quantity="other">%d months left</item>
    </plurals>
    <string name="about">"5G SMART VPN  offers secure and seamless browsing experience for Android users. With its advanced encryption technology, users can connect to the internet safely, ensuring their data remains private and protected from cyber threats. Whether accessing geo-restricted content or simply ensuring online privacy, 5G SMART VPN  provides fast and reliable connectivity with an intuitive interface, making it the ultimate choice for safeguarding your online activities."</string>
    <string name="app_name">5G SMART VPN </string>
    <string name="connection_close_confirm">Disconnect current VPN Connection?</string>
    <string name="first_fragment_label">First Fragment</string>
    <string name="hello_blank_fragment">Hello blank fragment</string>
    <string name="love">Made With <font color="red">❤️</font> in Bangladesh</string>
    <string name="next">Next</string>
    <string name="no">NO</string>
    <string name="previous">Previous</string>
    <string name="search">Search Server</string>
    <string name="search_server">Search Server</string>
    <string name="second_fragment_label">Second Fragment</string>
    <string name="upgrade_with_premium_plan_and_enjoy_premium_features_and_ads_free_experience">UpgradePremium Plan and Enjoy Premium features and Ads free experience</string>
    <string name="yes">YES</string>
    <style name="AppThemeDark" parent="Theme.AppCompat.DayNight.NoActionBar">
        <item name="colorPrimary">@color/black</item>
        <item name="colorPrimaryDark">@color/black</item>
        <item name="colorAccent">@color/black</item>
    </style>
    <style name="Base.Theme._5GSMARTVPNInfo" parent="Theme.Material3.Light.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>
    <style name="CustomSearchViewStyle" parent="Widget.AppCompat.SearchView">
        
        <item name="android:textColorHint">@color/black</item>
    </style>
    <style name="Normal">
        <item name="android:textColor">@color/white</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">12sp</item>
    </style>
    <style name="RatingBar" parent="@style/Theme.AppCompat">
        <item name="colorControlActivated">@color/colorAccent</item>
        <item name="colorControlNormal">@color/gray</item>
    </style>
    <style name="Theme._5GMasterVpn" parent="Base.Theme._5GSMARTVPNInfo"/>
    <style name="Title">
        <item name="android:textColor">@color/black</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">14sp</item>
    </style>
    <style name="dialog" parent="android:Theme.DeviceDefault.Light.Dialog"/>
</resources>