// Generated by view binder compiler. Do not edit!
package com.official.fivegfastvpn.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.official.fivegfastvpn.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemNotificationBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final CardView cardView;

  @NonNull
  public final TextView textCategory;

  @NonNull
  public final TextView textMessage;

  @NonNull
  public final TextView textPriority;

  @NonNull
  public final TextView textTime;

  @NonNull
  public final TextView textTitle;

  @NonNull
  public final View unreadIndicator;

  private ItemNotificationBinding(@NonNull CardView rootView, @NonNull CardView cardView,
      @NonNull TextView textCategory, @NonNull TextView textMessage, @NonNull TextView textPriority,
      @NonNull TextView textTime, @NonNull TextView textTitle, @NonNull View unreadIndicator) {
    this.rootView = rootView;
    this.cardView = cardView;
    this.textCategory = textCategory;
    this.textMessage = textMessage;
    this.textPriority = textPriority;
    this.textTime = textTime;
    this.textTitle = textTitle;
    this.unreadIndicator = unreadIndicator;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemNotificationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemNotificationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_notification, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemNotificationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      CardView cardView = (CardView) rootView;

      id = R.id.textCategory;
      TextView textCategory = ViewBindings.findChildViewById(rootView, id);
      if (textCategory == null) {
        break missingId;
      }

      id = R.id.textMessage;
      TextView textMessage = ViewBindings.findChildViewById(rootView, id);
      if (textMessage == null) {
        break missingId;
      }

      id = R.id.textPriority;
      TextView textPriority = ViewBindings.findChildViewById(rootView, id);
      if (textPriority == null) {
        break missingId;
      }

      id = R.id.textTime;
      TextView textTime = ViewBindings.findChildViewById(rootView, id);
      if (textTime == null) {
        break missingId;
      }

      id = R.id.textTitle;
      TextView textTitle = ViewBindings.findChildViewById(rootView, id);
      if (textTitle == null) {
        break missingId;
      }

      id = R.id.unreadIndicator;
      View unreadIndicator = ViewBindings.findChildViewById(rootView, id);
      if (unreadIndicator == null) {
        break missingId;
      }

      return new ItemNotificationBinding((CardView) rootView, cardView, textCategory, textMessage,
          textPriority, textTime, textTitle, unreadIndicator);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
