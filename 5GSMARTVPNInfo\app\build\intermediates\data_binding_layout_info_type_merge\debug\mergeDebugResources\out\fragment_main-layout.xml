<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_main" modulePackage="com.official.fivegfastvpn" filePath="app\src\main\res\layout\fragment_main.xml" directory="layout" isMerge="false" isBindingData="true" rootNodeType="androidx.core.widget.NestedScrollView"><Targets><Target tag="layout/fragment_main_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="4" startOffset="4" endLine="334" endOffset="43"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="9" startOffset="8" endLine="333" endOffset="22"/></Target><Target tag="binding_1" include="native_item_ads_container"><Expressions/><location startLine="330" startOffset="12" endLine="330" endOffset="65"/></Target><Target id="@+id/toolbar" view="RelativeLayout"><Expressions/><location startLine="20" startOffset="16" endLine="84" endOffset="32"/></Target><Target id="@+id/category" view="ImageView"><Expressions/><location startLine="25" startOffset="20" endLine="32" endOffset="54"/></Target><Target id="@+id/premium" view="ImageView"><Expressions/><location startLine="45" startOffset="20" endLine="51" endOffset="57"/></Target><Target id="@+id/pre" view="LinearLayout"><Expressions/><location startLine="53" startOffset="20" endLine="65" endOffset="34"/></Target><Target id="@+id/purchase_layout" view="LinearLayout"><Expressions/><location startLine="67" startOffset="20" endLine="81" endOffset="34"/></Target><Target id="@+id/currentConnectionLayout" view="RelativeLayout"><Expressions/><location startLine="96" startOffset="20" endLine="159" endOffset="36"/></Target><Target id="@+id/selectedServerIcon" view="ImageView"><Expressions/><location startLine="104" startOffset="24" endLine="111" endOffset="62"/></Target><Target id="@+id/countryName" view="TextView"><Expressions/><location startLine="119" startOffset="28" endLine="131" endOffset="57"/></Target><Target id="@+id/ipTv" view="TextView"><Expressions/><location startLine="133" startOffset="28" endLine="145" endOffset="59"/></Target><Target id="@+id/chevronRight" view="ImageView"><Expressions/><location startLine="149" startOffset="24" endLine="157" endOffset="70"/></Target><Target id="@+id/protectionStatus" view="TextView"><Expressions/><location startLine="161" startOffset="20" endLine="175" endOffset="49"/></Target><Target id="@+id/contime1" view="LinearLayout"><Expressions/><location startLine="177" startOffset="20" endLine="209" endOffset="34"/></Target><Target id="@+id/durationTv" view="TextView"><Expressions/><location startLine="185" startOffset="24" endLine="196" endOffset="58"/></Target><Target id="@+id/ipAddress" view="TextView"><Expressions/><location startLine="198" startOffset="24" endLine="207" endOffset="53"/></Target><Target id="@+id/statsLayout" view="LinearLayout"><Expressions/><location startLine="211" startOffset="20" endLine="270" endOffset="34"/></Target><Target id="@+id/byteInTv" view="TextView"><Expressions/><location startLine="227" startOffset="28" endLine="234" endOffset="58"/></Target><Target id="@+id/byteOutTv" view="TextView"><Expressions/><location startLine="252" startOffset="28" endLine="259" endOffset="58"/></Target><Target id="@+id/btnConnect" view="com.airbnb.lottie.LottieAnimationView"><Expressions/><location startLine="272" startOffset="20" endLine="281" endOffset="58"/></Target><Target id="@+id/logTv" view="TextView"><Expressions/><location startLine="283" startOffset="20" endLine="292" endOffset="49"/></Target><Target id="@+id/renewButtonLayout" view="LinearLayout"><Expressions/><location startLine="295" startOffset="20" endLine="321" endOffset="34"/></Target><Target id="@+id/btnRenew" view="Button"><Expressions/><location startLine="305" startOffset="24" endLine="311" endOffset="53"/></Target><Target id="@+id/extraTime" view="Button"><Expressions/><location startLine="312" startOffset="24" endLine="320" endOffset="53"/></Target></Targets></Layout>