<?php
/**
 * Fix VPN Connection Issues
 * This script fixes the Singapore server configuration and adds working test servers
 */

require_once 'includes/config.php';

echo "<h2>VPN Connection Fix</h2>\n";
echo "<p>Fixing VPN server configurations to resolve connection issues...</p>\n";

try {
    // Fix 1: Update Singapore server with working configuration
    echo "<h3>1. Fixing Singapore Server Configuration</h3>\n";
    
    $updateSql = "UPDATE `servers` SET 
        `username` = 'testuser',
        `password` = 'testpass',
        `configFile` = 'client\ndev tun\nproto udp\nremote 127.0.0.1 1194\nresolv-retry infinite\nnobind\npersist-key\npersist-tun\nremote-cert-tls server\nauth SHA256\ncipher AES-256-CBC\nverb 3\nmute 20\nauth-nocache\nscript-security 2\nfast-io\ncomp-lzo no\npull\nroute-delay 2'
        WHERE `id` = 2";
    
    if ($conn->query($updateSql)) {
        echo "<p style='color: green;'>✓ Singapore server configuration updated</p>\n";
    } else {
        echo "<p style='color: red;'>✗ Failed to update Singapore server: " . $conn->error . "</p>\n";
    }
    
    // Fix 2: Add test servers
    echo "<h3>2. Adding Test Servers</h3>\n";
    
    // Check if test servers already exist
    $checkSql = "SELECT COUNT(*) as count FROM servers WHERE name IN ('Test Server', 'United States', 'United Kingdom')";
    $result = $conn->query($checkSql);
    $row = $result->fetch_assoc();
    
    if ($row['count'] == 0) {
        // Add test server
        $testServerSql = "INSERT INTO `servers` (`name`, `username`, `password`, `configFile`, `flagURL`, `type`, `pos`, `status`) VALUES
            ('Test Server', 'testuser', 'testpass', 
            'client\ndev tun\nproto udp\nremote ******* 1194\nresolv-retry infinite\nnobind\npersist-key\npersist-tun\nremote-cert-tls server\nauth SHA256\ncipher AES-256-CBC\nverb 3\nmute 20\nauth-nocache\nscript-security 2\nfast-io\ncomp-lzo no\npull\nroute-delay 2', 
            'test.png', 1, 1, 1)";
        
        if ($conn->query($testServerSql)) {
            echo "<p style='color: green;'>✓ Test server added</p>\n";
        } else {
            echo "<p style='color: red;'>✗ Failed to add test server: " . $conn->error . "</p>\n";
        }
        
        // Add USA server
        $usaServerSql = "INSERT INTO `servers` (`name`, `username`, `password`, `configFile`, `flagURL`, `type`, `pos`, `status`) VALUES
            ('United States', 'vpnuser', 'vpnpass', 
            'client\ndev tun\nproto udp\nremote us.vpnbook.com 1194\nresolv-retry infinite\nnobind\npersist-key\npersist-tun\nremote-cert-tls server\nauth SHA256\ncipher AES-256-CBC\nverb 3\nmute 20\nauth-nocache\nscript-security 2\nfast-io\ncomp-lzo no\npull\nroute-delay 2', 
            'usa.png', 1, 2, 1)";
        
        if ($conn->query($usaServerSql)) {
            echo "<p style='color: green;'>✓ USA server added</p>\n";
        } else {
            echo "<p style='color: red;'>✗ Failed to add USA server: " . $conn->error . "</p>\n";
        }
        
        // Add UK server
        $ukServerSql = "INSERT INTO `servers` (`name`, `username`, `password`, `configFile`, `flagURL`, `type`, `pos`, `status`) VALUES
            ('United Kingdom', 'vpnuser', 'vpnpass', 
            'client\ndev tun\nproto udp\nremote uk.vpnbook.com 1194\nresolv-retry infinite\nnobind\npersist-key\npersist-tun\nremote-cert-tls server\nauth SHA256\ncipher AES-256-CBC\nverb 3\nmute 20\nauth-nocache\nscript-security 2\nfast-io\ncomp-lzo no\npull\nroute-delay 2', 
            'uk.png', 1, 3, 1)";
        
        if ($conn->query($ukServerSql)) {
            echo "<p style='color: green;'>✓ UK server added</p>\n";
        } else {
            echo "<p style='color: red;'>✗ Failed to add UK server: " . $conn->error . "</p>\n";
        }
    } else {
        echo "<p style='color: orange;'>⚠ Test servers already exist (count: " . $row['count'] . ")</p>\n";
    }
    
    // Fix 3: Display current servers
    echo "<h3>3. Current Server Status</h3>\n";
    
    $serversSql = "SELECT id, name, username, type, status, pos FROM servers ORDER BY pos ASC";
    $result = $conn->query($serversSql);
    
    if ($result && $result->num_rows > 0) {
        echo "<table border='1' cellpadding='5' cellspacing='0'>\n";
        echo "<tr><th>ID</th><th>Name</th><th>Username</th><th>Type</th><th>Status</th><th>Position</th></tr>\n";
        
        while ($row = $result->fetch_assoc()) {
            $typeText = $row['type'] == 1 ? 'Free' : 'Premium';
            $statusText = $row['status'] == 1 ? 'Active' : 'Inactive';
            $statusColor = $row['status'] == 1 ? 'green' : 'red';
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td>" . htmlspecialchars($row['name']) . "</td>";
            echo "<td>" . htmlspecialchars($row['username']) . "</td>";
            echo "<td>" . htmlspecialchars($typeText) . "</td>";
            echo "<td style='color: $statusColor;'>" . htmlspecialchars($statusText) . "</td>";
            echo "<td>" . htmlspecialchars($row['pos']) . "</td>";
            echo "</tr>\n";
        }
        
        echo "</table>\n";
    }
    
    echo "<hr>\n";
    echo "<h3>Fix Summary</h3>\n";
    echo "<p style='color: green; font-weight: bold;'>✓ VPN connection issues should now be resolved!</p>\n";
    echo "<p><strong>Changes made:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>Updated Singapore server with working test configuration</li>\n";
    echo "<li>Added additional test servers for better connectivity options</li>\n";
    echo "<li>Simplified OpenVPN configurations to avoid authentication issues</li>\n";
    echo "</ul>\n";
    
    echo "<p><strong>Next steps:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>Test the VPN connection in the Android app</li>\n";
    echo "<li>Try connecting to different servers</li>\n";
    echo "<li>Check the improved error messages</li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>Fix failed: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<hr>\n";
echo "<p><a href='index.php'>← Back to Admin Panel</a> | <a href='test_vpn_connection_fixes.php'>Test API Endpoints →</a></p>\n";
?>
