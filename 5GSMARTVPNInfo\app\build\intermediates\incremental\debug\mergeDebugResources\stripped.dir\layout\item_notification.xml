<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    android:foreground="?attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Unread Indicator -->
        <View
            android:id="@+id/unreadIndicator"
            android:layout_width="4dp"
            android:layout_height="match_parent"
            android:background="#3B82F6"
            android:layout_marginEnd="12dp"
            android:visibility="visible" />

        <!-- Content -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Header Row -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="4dp">

                <!-- Title -->
                <TextView
                    android:id="@+id/textTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Notification Title"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:maxLines="2"
                    android:ellipsize="end" />

                <!-- Time -->
                <TextView
                    android:id="@+id/textTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2h ago"
                    android:textSize="12sp"
                    android:textColor="#999999"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

            <!-- Message -->
            <TextView
                android:id="@+id/textMessage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="This is the notification message content that will be displayed to the user."
                android:textSize="14sp"
                android:textColor="#666666"
                android:maxLines="3"
                android:ellipsize="end"
                android:layout_marginBottom="8dp" />

            <!-- Tags Row -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="start|center_vertical">

                <!-- Category Tag -->
                <TextView
                    android:id="@+id/textCategory"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="GENERAL"
                    android:textSize="10sp"
                    android:textStyle="bold"
                    android:textColor="#3B82F6"
                    android:background="@drawable/category_badge_bg"
                    android:paddingLeft="8dp"
                    android:paddingRight="8dp"
                    android:paddingTop="2dp"
                    android:paddingBottom="2dp"
                    android:layout_marginEnd="8dp"
                    android:visibility="visible" />

                <!-- Priority Tag -->
                <TextView
                    android:id="@+id/textPriority"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="HIGH"
                    android:textSize="10sp"
                    android:textStyle="bold"
                    android:textColor="#F59E0B"
                    android:background="@drawable/priority_badge_bg"
                    android:paddingLeft="8dp"
                    android:paddingRight="8dp"
                    android:paddingTop="2dp"
                    android:paddingBottom="2dp"
                    android:visibility="gone" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
