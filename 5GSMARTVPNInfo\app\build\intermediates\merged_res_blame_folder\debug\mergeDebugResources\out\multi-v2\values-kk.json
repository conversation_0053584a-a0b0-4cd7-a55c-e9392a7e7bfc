{"logs": [{"outputFile": "com.official.fivegfastvpn.app-mergeDebugResources-75:/values-kk/values-kk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e1c57ccd2dc8ee4086206221e4fbff22\\transformed\\material-1.12.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,351,427,506,600,688,780,892,974,1034,1098,1193,1263,1326,1433,1498,1565,1626,1693,1755,1809,1923,1982,2043,2097,2172,2298,2386,2472,2573,2663,2753,2895,2967,3040,3177,3266,3347,3404,3460,3526,3597,3674,3745,3825,3897,3973,4054,4124,4224,4311,4383,4474,4567,4641,4716,4808,4860,4942,5008,5092,5178,5240,5304,5367,5436,5540,5644,5738,5838,5899,5959,6043,6127,6203", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,78,93,87,91,111,81,59,63,94,69,62,106,64,66,60,66,61,53,113,58,60,53,74,125,87,85,100,89,89,141,71,72,136,88,80,56,55,65,70,76,70,79,71,75,80,69,99,86,71,90,92,73,74,91,51,81,65,83,85,61,63,62,68,103,103,93,99,60,59,83,83,75,78", "endOffsets": "268,346,422,501,595,683,775,887,969,1029,1093,1188,1258,1321,1428,1493,1560,1621,1688,1750,1804,1918,1977,2038,2092,2167,2293,2381,2467,2568,2658,2748,2890,2962,3035,3172,3261,3342,3399,3455,3521,3592,3669,3740,3820,3892,3968,4049,4119,4219,4306,4378,4469,4562,4636,4711,4803,4855,4937,5003,5087,5173,5235,5299,5362,5431,5535,5639,5733,5833,5894,5954,6038,6122,6198,6277"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,149,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3014,3092,3168,3247,3341,4148,4240,4352,6751,6811,6875,7287,7357,7420,7527,7592,7659,7720,7787,7849,7903,8017,8076,8137,8191,8266,8392,8480,8566,8667,8757,8847,8989,9061,9134,9271,9360,9441,9498,9554,9620,9691,9768,9839,9919,9991,10067,10148,10218,10318,10405,10477,10568,10661,10735,10810,10902,10954,11036,11102,11186,11272,11334,11398,11461,11530,11634,11738,11832,11932,11993,13522,14102,14186,14262", "endLines": "5,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,149,158,159,160", "endColumns": "12,77,75,78,93,87,91,111,81,59,63,94,69,62,106,64,66,60,66,61,53,113,58,60,53,74,125,87,85,100,89,89,141,71,72,136,88,80,56,55,65,70,76,70,79,71,75,80,69,99,86,71,90,92,73,74,91,51,81,65,83,85,61,63,62,68,103,103,93,99,60,59,83,83,75,78", "endOffsets": "318,3087,3163,3242,3336,3424,4235,4347,4429,6806,6870,6965,7352,7415,7522,7587,7654,7715,7782,7844,7898,8012,8071,8132,8186,8261,8387,8475,8561,8662,8752,8842,8984,9056,9129,9266,9355,9436,9493,9549,9615,9686,9763,9834,9914,9986,10062,10143,10213,10313,10400,10472,10563,10656,10730,10805,10897,10949,11031,11097,11181,11267,11329,11393,11456,11525,11629,11733,11827,11927,11988,12048,13601,14181,14257,14336"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\abafdc52b4a83dcb3e4911636b323609\\transformed\\play-services-base-18.5.0\\res\\values-kk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,441,563,665,801,923,1042,1147,1308,1410,1563,1688,1837,1990,2049,2104", "endColumns": "98,148,121,101,135,121,118,104,160,101,152,124,148,152,58,54,73", "endOffsets": "291,440,562,664,800,922,1041,1146,1307,1409,1562,1687,1836,1989,2048,2103,2177"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4434,4537,4690,4816,4922,5062,5188,5311,5584,5749,5855,6012,6141,6294,6451,6514,6573", "endColumns": "102,152,125,105,139,125,122,108,164,105,156,128,152,156,62,58,77", "endOffsets": "4532,4685,4811,4917,5057,5183,5306,5415,5744,5850,6007,6136,6289,6446,6509,6568,6646"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4174daf14328b785510b24290d95f19a\\transformed\\play-services-ads-24.3.0\\res\\values-kk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,250,299,356,425,496,609,674,817,934,1065,1120,1179,1293,1381,1424,1518,1554,1592,1643,1726,1767", "endColumns": "50,48,56,68,70,112,64,142,116,130,54,58,113,87,42,93,35,37,50,82,40,55", "endOffsets": "249,298,355,424,495,608,673,816,933,1064,1119,1178,1292,1380,1423,1517,1553,1591,1642,1725,1766,1822"}, "to": {"startLines": "133,134,135,138,139,140,141,142,143,144,145,146,147,148,150,151,152,153,154,155,156,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12053,12108,12161,12453,12526,12601,12718,12787,12934,13055,13190,13249,13312,13430,13606,13653,13751,13791,13833,13888,13975,14442", "endColumns": "54,52,60,72,74,116,68,146,120,134,58,62,117,91,46,97,39,41,54,86,44,59", "endOffsets": "12103,12156,12217,12521,12596,12713,12782,12929,13050,13185,13244,13307,13425,13517,13648,13746,13786,13828,13883,13970,14015,14497"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\259d0aa544e023aed46b172a705803dc\\transformed\\appcompat-1.7.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,2796", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,2873"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,536,646,731,837,956,1036,1113,1204,1297,1392,1486,1586,1679,1774,1871,1962,2053,2134,2239,2342,2440,2547,2653,2753,2919,14020", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "426,531,641,726,832,951,1031,1108,1199,1292,1387,1481,1581,1674,1769,1866,1957,2048,2129,2234,2337,2435,2542,2648,2748,2914,3009,14097"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a056806aa4aaa5631c39921f8bed3647\\transformed\\navigation-ui-2.9.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,117", "endOffsets": "163,281"}, "to": {"startLines": "136,137", "startColumns": "4,4", "startOffsets": "12222,12335", "endColumns": "112,117", "endOffsets": "12330,12448"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b1798530a3a51669983781b25eadf6b2\\transformed\\browser-1.8.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,259,367", "endColumns": "99,103,107,104", "endOffsets": "150,254,362,467"}, "to": {"startLines": "66,70,71,72", "startColumns": "4,4,4,4", "startOffsets": "6651,6970,7074,7182", "endColumns": "99,103,107,104", "endOffsets": "6746,7069,7177,7282"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b5335a6eacc5065e32155bfa643f759a\\transformed\\core-1.16.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,359,462,566,663,774", "endColumns": "99,101,101,102,103,96,110,100", "endOffsets": "150,252,354,457,561,658,769,870"}, "to": {"startLines": "38,39,40,41,42,43,44,161", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3429,3529,3631,3733,3836,3940,4037,14341", "endColumns": "99,101,101,102,103,96,110,100", "endOffsets": "3524,3626,3728,3831,3935,4032,4143,14437"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1aae7574d5734885cc339c3642ba94ef\\transformed\\play-services-basement-18.4.0\\res\\values-kk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5420", "endColumns": "163", "endOffsets": "5579"}}]}]}