<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_notifications" modulePackage="com.official.fivegfastvpn" filePath="app\src\main\res\layout\activity_notifications.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_notifications_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="136" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="16" endOffset="48"/></Target><Target id="@+id/markAllReadButton" view="TextView"><Expressions/><location startLine="36" startOffset="8" endLine="48" endOffset="39"/></Target><Target id="@+id/swipeRefreshLayout" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="59" startOffset="8" endLine="72" endOffset="63"/></Target><Target id="@+id/recyclerViewNotifications" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="64" startOffset="12" endLine="70" endOffset="47"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="75" startOffset="8" endLine="80" endOffset="39"/></Target><Target id="@+id/emptyView" view="LinearLayout"><Expressions/><location startLine="83" startOffset="8" endLine="132" endOffset="22"/></Target><Target id="@+id/emptyMessage" view="TextView"><Expressions/><location startLine="108" startOffset="12" endLine="117" endOffset="52"/></Target><Target id="@+id/retryButton" view="Button"><Expressions/><location startLine="119" startOffset="12" endLine="130" endOffset="43"/></Target></Targets></Layout>