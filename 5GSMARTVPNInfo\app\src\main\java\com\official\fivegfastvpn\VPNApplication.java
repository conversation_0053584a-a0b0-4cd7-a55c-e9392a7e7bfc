package com.official.fivegfastvpn;

import android.app.Activity;
import android.app.Application;

import com.official.fivegfastvpn.ads.AppOpenManager;

public class VPNApplication extends Application {
    private static AppOpenManager appOpenManager;

    @Override
    public void onCreate() {
        super.onCreate();
        appOpenManager = new AppOpenManager(this);
    }

    public void initializeAppOpenAd() {
        if (appOpenManager != null) {
            appOpenManager.initializeAd();
        }
    }

    public void showAdIfAvailable(Activity activity) {
        if (appOpenManager != null) {
            appOpenManager.showAdFromSplash(activity);
        }
    }

    public void setAdLoadCallback(AppOpenManager.OnAppOpenAdLoadCallback callback) {
        if (appOpenManager != null) {
            appOpenManager.setAdLoadCallback(callback);
        }
    }
}
