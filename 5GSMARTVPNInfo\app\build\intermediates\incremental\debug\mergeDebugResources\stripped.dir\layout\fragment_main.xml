
                                                       
                                                   

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/vpnhome" android:tag="layout/fragment_main_0" xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:tools="http://schemas.android.com/tools">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical" android:tag="binding_1">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <RelativeLayout
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize">

                    <ImageView
                        android:id="@+id/category"
                        android:layout_width="60dp"
                        android:layout_height="match_parent"
                        android:layout_alignParentLeft="true"
                        android:layout_marginEnd="14dp"
                        android:padding="12dp"
                        android:src="@drawable/menu" />

                    <TextView
                        style="@style/Normal"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="65dp"
                        android:layout_centerVertical="true"
                        android:fontFamily="sans-serif"
                        android:text="@string/app_name"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="bold" />
                    <ImageView
                        android:id="@+id/premium"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_alignParentRight="true"

                        android:src="@drawable/premium" />

                    <LinearLayout
                        android:id="@+id/pre"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="5dp"
                        android:orientation="horizontal"
                        android:paddingStart="10dp"
                        android:paddingEnd="10dp"
                        android:paddingBottom="10dp">

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/purchase_layout"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentStart="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="5dp"
                        android:orientation="horizontal"
                        android:paddingStart="10dp"
                        android:paddingEnd="10dp"
                        android:paddingBottom="10dp"
                        android:visibility="invisible">


                    </LinearLayout>


                </RelativeLayout>


                <RelativeLayout

                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:paddingStart="10dp"
                    android:paddingEnd="10dp">


                    <RelativeLayout
                        android:id="@+id/currentConnectionLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="7dp"
                        android:background="@drawable/serverselect"
                        android:clickable="true">

                        <ImageView
                            android:id="@+id/selectedServerIcon"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="35dp"
                            android:padding="2dp"
                            android:src="@drawable/ic_japan" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/countryName"
                                style="@style/Title"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:ellipsize="end"
                                android:fontFamily="sans-serif-medium"
                                android:maxLength="14"
                                android:maxLines="2"
                                android:text="Country"
                                android:textColor="@color/white"
                                android:textSize="18dp" />

                            <TextView
                                android:id="@+id/ipTv"
                                style="@style/Normal"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_gravity="center"
                                android:fontFamily="sans-serif-medium"
                                android:padding="2dp"
                                android:text="*************"
                                android:textColor="@color/white"
                                android:textSize="17sp"
                                android:textStyle="bold"
                                android:visibility="gone" />

                        </LinearLayout>

                        <ImageView
                            android:id="@+id/chevronRight"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="16dp"
                            android:visibility="gone"
                            android:src="@drawable/ic_chevron_right" />

                    </RelativeLayout>

                    <TextView
                        android:id="@+id/protectionStatus"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@id/currentConnectionLayout"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="16dp"
                        android:background="@drawable/protection_status_bg"
                        android:paddingStart="16dp"
                        android:paddingTop="8dp"
                        android:paddingEnd="16dp"
                        android:paddingBottom="8dp"
                        android:text="You are not protected"
                        android:textColor="@color/white"
                        android:textSize="14sp" />

                    <LinearLayout
                        android:id="@+id/contime1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@id/protectionStatus"
                        android:layout_marginTop="16dp"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/durationTv"
                            style="@style/Normal"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:fontFamily="sans-serif-medium"
                            android:text="00:28:40"
                            android:textColor="@color/white"
                            android:textSize="40sp"
                            android:textStyle="bold"
                            android:visibility="visible" />

                        <TextView
                            android:id="@+id/ipAddress"
                            style="@style/Normal"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginTop="4dp"
                            android:text="************"
                            android:textColor="@color/white"
                            android:textSize="16sp" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/statsLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@id/contime1"
                        android:layout_marginTop="16dp"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="32dp"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/byteInTv"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="82.4"
                                android:textColor="@color/white"
                                android:textSize="24sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Download kB"
                                android:textColor="@color/white"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="32dp"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/byteOutTv"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="24.0"
                                android:textColor="@color/white"
                                android:textSize="24sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Upload kB"
                                android:textColor="@color/white"
                                android:textSize="12sp" />

                        </LinearLayout>

                    </LinearLayout>

                    <com.airbnb.lottie.LottieAnimationView
                        android:id="@+id/btnConnect"
                        android:layout_width="200dp"
                        android:layout_height="200dp"
                        android:layout_below="@id/statsLayout"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="16dp"
                        app:lottie_autoPlay="true"
                        app:lottie_loop="true"
                        app:lottie_rawRes="@raw/clottie" />

                    <TextView
                        android:id="@+id/logTv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@id/btnConnect"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="8dp"
                        android:text="Tap to connect"
                        android:textColor="@color/white"
                        android:textSize="16sp" />


                    <LinearLayout
                        android:id="@+id/renewButtonLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@id/logTv"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal"
                        android:visibility="gone"
                        android:gravity="center">
                        <Button
                            android:id="@+id/btnRenew"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Renew"
                            android:textColor="@color/white"
                            android:textSize="16sp" />
                        <Button
                            android:id="@+id/extraTime"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="10dp"
                            android:text="Extra Time"
                            android:visibility="gone"
                            android:textColor="@color/white"
                            android:textSize="16sp" />
                    </LinearLayout>


                </RelativeLayout>


            </LinearLayout>


            <include layout="@layout/native_item_ads_container" />


        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
         