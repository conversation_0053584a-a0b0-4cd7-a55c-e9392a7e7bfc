{"logs": [{"outputFile": "com.official.fivegfastvpn.app-mergeDebugResources-75:/values-fa/values-fa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1aae7574d5734885cc339c3642ba94ef\\transformed\\play-services-basement-18.4.0\\res\\values-fa\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "150", "endOffsets": "345"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5426", "endColumns": "154", "endOffsets": "5576"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e1c57ccd2dc8ee4086206221e4fbff22\\transformed\\material-1.12.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,334,411,493,586,673,770,899,983,1041,1104,1194,1263,1323,1414,1477,1541,1600,1667,1729,1784,1907,1965,2026,2081,2153,2290,2371,2451,2548,2629,2711,2841,2915,2989,3121,3207,3284,3335,3389,3455,3526,3603,3674,3753,3826,3900,3970,4044,4145,4231,4305,4394,4486,4560,4633,4722,4773,4853,4920,5003,5087,5149,5213,5276,5345,5439,5540,5633,5731,5786,5844,5922,6008,6085", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,74,76,81,92,86,96,128,83,57,62,89,68,59,90,62,63,58,66,61,54,122,57,60,54,71,136,80,79,96,80,81,129,73,73,131,85,76,50,53,65,70,76,70,78,72,73,69,73,100,85,73,88,91,73,72,88,50,79,66,82,83,61,63,62,68,93,100,92,97,54,57,77,85,76,73", "endOffsets": "254,329,406,488,581,668,765,894,978,1036,1099,1189,1258,1318,1409,1472,1536,1595,1662,1724,1779,1902,1960,2021,2076,2148,2285,2366,2446,2543,2624,2706,2836,2910,2984,3116,3202,3279,3330,3384,3450,3521,3598,3669,3748,3821,3895,3965,4039,4140,4226,4300,4389,4481,4555,4628,4717,4768,4848,4915,4998,5082,5144,5208,5271,5340,5434,5535,5628,5726,5781,5839,5917,6003,6080,6154"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,149,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2999,3074,3151,3233,3326,4137,4234,4363,6772,6830,6893,7294,7363,7423,7514,7577,7641,7700,7767,7829,7884,8007,8065,8126,8181,8253,8390,8471,8551,8648,8729,8811,8941,9015,9089,9221,9307,9384,9435,9489,9555,9626,9703,9774,9853,9926,10000,10070,10144,10245,10331,10405,10494,10586,10660,10733,10822,10873,10953,11020,11103,11187,11249,11313,11376,11445,11539,11640,11733,11831,11886,13373,13916,14002,14079", "endLines": "5,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,149,158,159,160", "endColumns": "12,74,76,81,92,86,96,128,83,57,62,89,68,59,90,62,63,58,66,61,54,122,57,60,54,71,136,80,79,96,80,81,129,73,73,131,85,76,50,53,65,70,76,70,78,72,73,69,73,100,85,73,88,91,73,72,88,50,79,66,82,83,61,63,62,68,93,100,92,97,54,57,77,85,76,73", "endOffsets": "304,3069,3146,3228,3321,3408,4229,4358,4442,6825,6888,6978,7358,7418,7509,7572,7636,7695,7762,7824,7879,8002,8060,8121,8176,8248,8385,8466,8546,8643,8724,8806,8936,9010,9084,9216,9302,9379,9430,9484,9550,9621,9698,9769,9848,9921,9995,10065,10139,10240,10326,10400,10489,10581,10655,10728,10817,10868,10948,11015,11098,11182,11244,11308,11371,11440,11534,11635,11728,11826,11881,11939,13446,13997,14074,14148"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4174daf14328b785510b24290d95f19a\\transformed\\play-services-ads-24.3.0\\res\\values-fa\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,247,292,350,418,487,592,654,804,921,1042,1095,1152,1261,1348,1387,1472,1507,1543,1589,1663,1703", "endColumns": "47,44,57,67,68,104,61,149,116,120,52,56,108,86,38,84,34,35,45,73,39,55", "endOffsets": "246,291,349,417,486,591,653,803,920,1041,1094,1151,1260,1347,1386,1471,1506,1542,1588,1662,1702,1758"}, "to": {"startLines": "133,134,135,138,139,140,141,142,143,144,145,146,147,148,150,151,152,153,154,155,156,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11944,11996,12045,12331,12403,12476,12585,12651,12805,12926,13051,13108,13169,13282,13451,13494,13583,13622,13662,13712,13790,14254", "endColumns": "51,48,61,71,72,108,65,153,120,124,56,60,112,90,42,88,38,39,49,77,43,59", "endOffsets": "11991,12040,12102,12398,12471,12580,12646,12800,12921,13046,13103,13164,13277,13368,13489,13578,13617,13657,13707,13785,13829,14309"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\abafdc52b4a83dcb3e4911636b323609\\transformed\\play-services-base-18.5.0\\res\\values-fa\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,450,575,674,810,932,1042,1140,1289,1395,1561,1688,1837,1989,2051,2115", "endColumns": "103,152,124,98,135,121,109,97,148,105,165,126,148,151,61,63,80", "endOffsets": "296,449,574,673,809,931,1041,1139,1288,1394,1560,1687,1836,1988,2050,2114,2195"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4447,4555,4712,4841,4944,5084,5210,5324,5581,5734,5844,6014,6145,6298,6454,6520,6588", "endColumns": "107,156,128,102,139,125,113,101,152,109,169,130,152,155,65,67,84", "endOffsets": "4550,4707,4836,4939,5079,5205,5319,5421,5729,5839,6009,6140,6293,6449,6515,6583,6668"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b1798530a3a51669983781b25eadf6b2\\transformed\\browser-1.8.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,154,251,362", "endColumns": "98,96,110,102", "endOffsets": "149,246,357,460"}, "to": {"startLines": "66,70,71,72", "startColumns": "4,4,4,4", "startOffsets": "6673,6983,7080,7191", "endColumns": "98,96,110,102", "endOffsets": "6767,7075,7186,7289"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\259d0aa544e023aed46b172a705803dc\\transformed\\appcompat-1.7.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,2795", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,419,520,631,715,816,931,1011,1088,1181,1276,1368,1462,1564,1659,1756,1850,1943,2033,2115,2223,2327,2425,2531,2636,2741,2898,13834", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "414,515,626,710,811,926,1006,1083,1176,1271,1363,1457,1559,1654,1751,1845,1938,2028,2110,2218,2322,2420,2526,2631,2736,2893,2994,13911"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b5335a6eacc5065e32155bfa643f759a\\transformed\\core-1.16.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "38,39,40,41,42,43,44,161", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3413,3512,3614,3713,3813,3914,4020,14153", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "3507,3609,3708,3808,3909,4015,4132,14249"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a056806aa4aaa5631c39921f8bed3647\\transformed\\navigation-ui-2.9.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,114", "endOffsets": "159,274"}, "to": {"startLines": "136,137", "startColumns": "4,4", "startOffsets": "12107,12216", "endColumns": "108,114", "endOffsets": "12211,12326"}}]}]}