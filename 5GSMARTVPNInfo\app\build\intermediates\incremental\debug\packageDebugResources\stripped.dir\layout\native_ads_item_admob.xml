<?xml version="1.0" encoding="utf-8" ?>
<com.google.android.gms.ads.nativead.NativeAdView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/llparent"
    android:layout_width="match_parent"
    android:layout_height="220dp"
    android:layout_gravity="center"
    android:gravity="center"
    android:tag="layout/row_video_list_native_ad_0">

    <androidx.cardview.widget.CardView
        android:id="@+id/cv_video_thumb"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        app:cardCornerRadius="5dp"
        app:cardElevation="0dp">

        <RelativeLayout
            android:id="@+id/rladView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:gravity="center"
            android:minHeight="100dp"
            android:orientation="vertical">

            <RelativeLayout
                android:id="@+id/rladtext"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_gravity="start"
                android:layout_marginStart="5dp"
                android:layout_marginTop="5dp"
                android:background="@drawable/round_corner"
                android:backgroundTint="#FFC041"
                android:padding="2dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:fontFamily="sans-serif"
                    android:gravity="center"
                    android:text="AD"
                    android:textAlignment="center"
                    android:textColor="@color/white"
                    android:textSize="8sp" />

            </RelativeLayout>

            <ImageView
                android:id="@+id/removeadd"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="3dp"
                android:layout_marginEnd="3dp"
                android:src="@drawable/ic_close"
                app:tint="@color/icon" />


            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_above="@+id/llbottom"
                android:layout_below="@+id/rladtext"
                android:layout_marginStart="5dp"
                android:layout_marginTop="5dp"
                android:layout_marginEnd="5dp"
                android:layout_marginBottom="5dp">

                <com.google.android.gms.ads.nativead.MediaView
                    android:id="@+id/ad_media"
                    android:layout_width="140dp"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true" />

                <TextView
                    android:id="@+id/ad_body"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_margin="10dp"
                    android:layout_toEndOf="@id/ad_media"
                    android:ellipsize="end"
                    android:maxLines="6"
                    android:textColor="@color/text"
                    android:textSize="12.0sp"
                    android:visibility="gone" />


            </RelativeLayout>


            <LinearLayout
                android:id="@+id/llbottom"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/llprice"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <TextView
                        android:id="@+id/ad_price"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingStart="5dp"
                        android:paddingLeft="5dp"
                        android:paddingEnd="5dp"
                        android:paddingRight="5dp"
                        android:textColor="@color/text"
                        android:textSize="12.0sp" />

                    <TextView
                        android:id="@+id/ad_store"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingStart="5dp"
                        android:paddingLeft="5dp"
                        android:paddingEnd="5dp"
                        android:paddingRight="5dp"
                        android:textColor="@color/text"
                        android:textSize="12.0sp" />
                </LinearLayout>

                <RelativeLayout
                    android:id="@+id/profile"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/ad_icon"
                        android:layout_width="22dp"
                        android:layout_height="22dp"
                        android:layout_centerVertical="true"
                        android:layout_marginTop="3dp"
                        android:scaleType="fitXY" />

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:layout_marginStart="5dp"
                        android:layout_marginEnd="5dp"
                        android:layout_toEndOf="@+id/ad_icon"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/ad_headline"
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:gravity="center_vertical"
                            android:maxLines="1"
                            android:text="@string/app_name"
                            android:textColor="@color/text"
                            android:textSize="11.0sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/ad_advertiser"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:gravity="center_vertical"
                            android:maxLines="1"
                            android:textColor="@color/text"
                            android:textSize="11.0sp"
                            android:textStyle="bold"
                            android:visibility="gone" />

                        <RatingBar
                            android:id="@+id/ad_stars"
                            style="?android:ratingBarStyleSmall"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:gravity="center_vertical"
                            android:isIndicator="true"
                            android:numStars="5"
                            android:stepSize="0.5"
                            android:theme="@style/RatingBar" />
                    </LinearLayout>
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rl_install"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:paddingTop="5dp">

                    <Button
                        android:id="@+id/ad_call_to_action"
                        android:layout_width="match_parent"
                        android:layout_height="35dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginEnd="20dp"
                        android:layout_marginBottom="5dp"
                        android:background="@drawable/btn_bg"
                        android:backgroundTint="@color/colorAccent"
                        android:fontFamily="sans-serif"
                        android:gravity="center"
                        android:textAlignment="center"
                        android:textColor="@color/white"
                        android:textSize="12.0sp"
                        android:textStyle="bold" />
                </RelativeLayout>
            </LinearLayout>

        </RelativeLayout>

    </androidx.cardview.widget.CardView>

</com.google.android.gms.ads.nativead.NativeAdView>
