<?php
/**
 * Run Server Migration Script
 * This script adds working VPN servers to improve connectivity
 */

require_once 'includes/config.php';

echo "<h2>5G Smart VPN - Server Migration</h2>\n";
echo "<p>Adding working VPN servers to improve connectivity...</p>\n";

try {
    // Read and execute the SQL file
    $sqlFile = __DIR__ . '/add_working_servers.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL file not found: " . $sqlFile);
    }
    
    $sql = file_get_contents($sqlFile);
    
    if ($sql === false) {
        throw new Exception("Failed to read SQL file");
    }
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue; // Skip empty statements and comments
        }
        
        try {
            $result = $conn->query($statement);
            if ($result) {
                $successCount++;
                echo "<p style='color: green;'>✓ Executed: " . substr($statement, 0, 50) . "...</p>\n";
            } else {
                $errorCount++;
                echo "<p style='color: red;'>✗ Failed: " . substr($statement, 0, 50) . "... - " . $conn->error . "</p>\n";
            }
        } catch (Exception $e) {
            $errorCount++;
            echo "<p style='color: red;'>✗ Error: " . substr($statement, 0, 50) . "... - " . $e->getMessage() . "</p>\n";
        }
    }
    
    echo "<hr>\n";
    echo "<h3>Migration Summary:</h3>\n";
    echo "<p>Successful statements: <strong>$successCount</strong></p>\n";
    echo "<p>Failed statements: <strong>$errorCount</strong></p>\n";
    
    if ($errorCount === 0) {
        echo "<p style='color: green; font-weight: bold;'>✓ Migration completed successfully!</p>\n";
    } else {
        echo "<p style='color: orange; font-weight: bold;'>⚠ Migration completed with some errors.</p>\n";
    }
    
    // Display current servers
    echo "<hr>\n";
    echo "<h3>Current VPN Servers:</h3>\n";
    
    $result = $conn->query("SELECT id, name, type, status, pos FROM servers ORDER BY pos ASC");
    
    if ($result && $result->num_rows > 0) {
        echo "<table border='1' cellpadding='5' cellspacing='0'>\n";
        echo "<tr><th>ID</th><th>Name</th><th>Type</th><th>Status</th><th>Position</th></tr>\n";
        
        while ($row = $result->fetch_assoc()) {
            $typeText = $row['type'] == 1 ? 'Free' : 'Premium';
            $statusText = $row['status'] == 1 ? 'Active' : 'Inactive';
            $statusColor = $row['status'] == 1 ? 'green' : 'red';
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td>" . htmlspecialchars($row['name']) . "</td>";
            echo "<td>" . htmlspecialchars($typeText) . "</td>";
            echo "<td style='color: $statusColor;'>" . htmlspecialchars($statusText) . "</td>";
            echo "<td>" . htmlspecialchars($row['pos']) . "</td>";
            echo "</tr>\n";
        }
        
        echo "</table>\n";
    } else {
        echo "<p style='color: red;'>No servers found in database.</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>Migration failed: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<hr>\n";
echo "<p><a href='index.php'>← Back to Admin Panel</a></p>\n";
?>
