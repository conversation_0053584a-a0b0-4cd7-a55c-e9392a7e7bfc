<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#F5F5F5">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@drawable/gradient"
        android:elevation="4dp"
        app:titleTextColor="@android:color/white"
        app:navigationIcon="@drawable/ic_back" />

    <!-- Header with <PERSON>ton -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@android:color/white"
        android:elevation="2dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Recent Notifications"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="#333333" />

        <TextView
            android:id="@+id/markAllReadButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Mark All Read"
            android:textColor="#3B82F6"
            android:textSize="14sp"
            android:textStyle="bold"
            android:padding="8dp"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:visibility="gone" />

    </LinearLayout>

    <!-- Main Content -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <!-- SwipeRefreshLayout with RecyclerView -->
        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipeRefreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewNotifications"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="8dp"
                android:clipToPadding="false"
                android:scrollbars="vertical" />

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

        <!-- Progress Bar -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone" />

        <!-- Empty State -->
        <LinearLayout
            android:id="@+id/emptyView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="32dp"
            android:visibility="gone">

            <ImageView
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:src="@drawable/ic_notification_empty"
                android:alpha="0.5"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="No Notifications"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="#666666"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/emptyMessage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="You're all caught up! New notifications will appear here."
                android:textSize="14sp"
                android:textColor="#999999"
                android:textAlignment="center"
                android:gravity="center"
                android:layout_marginBottom="16dp" />

            <Button
                android:id="@+id/retryButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Retry"
                android:textColor="@android:color/white"
                android:background="@drawable/btn_bg"
                android:paddingLeft="24dp"
                android:paddingRight="24dp"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                android:visibility="gone" />

        </LinearLayout>

    </FrameLayout>

</LinearLayout>
