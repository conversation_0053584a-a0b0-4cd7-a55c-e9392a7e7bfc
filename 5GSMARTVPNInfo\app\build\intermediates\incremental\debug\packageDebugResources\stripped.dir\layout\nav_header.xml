<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="130dp">

    <androidx.cardview.widget.CardView
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_margin="20dp"
        android:layout_centerInParent="true"
        app:cardCornerRadius="30dp">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@mipmap/ic_launcher" />


    </androidx.cardview.widget.CardView>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="10dp"
        android:layout_marginBottom="10dp"
        android:text="@string/app_name"
        android:layout_centerInParent="true"
        android:textColor="@color/white"
        android:textSize="14sp" />


</RelativeLayout>