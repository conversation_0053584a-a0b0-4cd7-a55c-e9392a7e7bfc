<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/tool_bg"
    android:layout_width="match_parent"
    android:layout_height="?attr/actionBarSize">


    <ImageView
        android:id="@+id/tool_back"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="10dp"
        android:layout_centerVertical="true"
        android:contentDescription="Open NavBar"
        android:padding="8dp"
        android:src="@drawable/ic_back" />

    <TextView
        android:id="@+id/tool_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_toEndOf="@id/tool_back"
        android:fontFamily="sans-serif-smallcaps"
        android:text="@string/app_name"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:textStyle="bold" />


</RelativeLayout>