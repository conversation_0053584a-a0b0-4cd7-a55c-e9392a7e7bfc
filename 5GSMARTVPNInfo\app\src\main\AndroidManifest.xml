<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
    <uses-permission android:name="com.android.vending.BILLING" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <application
        android:name=".VPNApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:usesCleartextTraffic="true"
        android:theme="@style/Base.Theme._5GSMARTVPNInfo"
        tools:targetApi="31">
        <activity
            android:name=".activity.About_Us"
            android:exported="false" />
        <activity
            android:name=".activity.NotificationsActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity" />
        <activity
            android:name=".api.ApiTestActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity" />

        <activity android:name=".pro.PremiumActivity" />
        <activity android:name=".MainActivity" />
        <activity android:name=".activity.ServersActivity" />
        <activity
            android:name=".SplashActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="de.blinkt.openvpn.DisconnectVPNActivity"
            android:excludeFromRecents="true"
            android:noHistory="true"
            android:taskAffinity=".DisconnectVPN"
            android:theme="@style/blinkt.dialog" />

        <service
            android:name="de.blinkt.openvpn.core.OpenVPNService"
            android:exported="true"
            android:foregroundServiceType="dataSync"
            android:permission="android.permission.BIND_VPN_SERVICE">
            <intent-filter>
                <action android:name="android.net.VpnService" />
            </intent-filter>
        </service>
        <service
            android:name=".MyFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-5193340328939721~2015388624" />

        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="" />

    <!-- Required: set your sentry.io project identifier (DSN) -->
    <meta-data android:name="io.sentry.dsn" android:value="https://<EMAIL>/4508793236488192" />

    <!-- enable automatic breadcrumbs for user interactions (clicks, swipes, scrolls) -->
    <meta-data android:name="io.sentry.traces.user-interaction.enable" android:value="true" />
    <!-- enable screenshot for crashes (could contain sensitive/PII data) -->
    <meta-data android:name="io.sentry.attach-screenshot" android:value="true" />
    <!-- enable view hierarchy for crashes -->
    <meta-data android:name="io.sentry.attach-view-hierarchy" android:value="true" />

    <!-- enable the performance API by setting a sample-rate, adjust in production env -->
    <meta-data android:name="io.sentry.traces.sample-rate" android:value="1.0" />
</application>

</manifest>