// Generated by view binder compiler. Do not edit!
package com.official.fivegfastvpn.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.facebook.ads.MediaView;
import com.official.fivegfastvpn.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class NativeAdItemFacebookBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout adChoicesContainer;

  @NonNull
  public final TextView nativeAdBody;

  @NonNull
  public final Button nativeAdCallToAction;

  @NonNull
  public final MediaView nativeAdIcon;

  @NonNull
  public final MediaView nativeAdMedia;

  @NonNull
  public final TextView nativeAdSocialContext;

  @NonNull
  public final TextView nativeAdSponsoredLabel;

  @NonNull
  public final TextView nativeAdTitle;

  private NativeAdItemFacebookBinding(@NonNull LinearLayout rootView,
      @NonNull LinearLayout adChoicesContainer, @NonNull TextView nativeAdBody,
      @NonNull Button nativeAdCallToAction, @NonNull MediaView nativeAdIcon,
      @NonNull MediaView nativeAdMedia, @NonNull TextView nativeAdSocialContext,
      @NonNull TextView nativeAdSponsoredLabel, @NonNull TextView nativeAdTitle) {
    this.rootView = rootView;
    this.adChoicesContainer = adChoicesContainer;
    this.nativeAdBody = nativeAdBody;
    this.nativeAdCallToAction = nativeAdCallToAction;
    this.nativeAdIcon = nativeAdIcon;
    this.nativeAdMedia = nativeAdMedia;
    this.nativeAdSocialContext = nativeAdSocialContext;
    this.nativeAdSponsoredLabel = nativeAdSponsoredLabel;
    this.nativeAdTitle = nativeAdTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static NativeAdItemFacebookBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static NativeAdItemFacebookBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.native_ad_item_facebook, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static NativeAdItemFacebookBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ad_choices_container;
      LinearLayout adChoicesContainer = ViewBindings.findChildViewById(rootView, id);
      if (adChoicesContainer == null) {
        break missingId;
      }

      id = R.id.native_ad_body;
      TextView nativeAdBody = ViewBindings.findChildViewById(rootView, id);
      if (nativeAdBody == null) {
        break missingId;
      }

      id = R.id.native_ad_call_to_action;
      Button nativeAdCallToAction = ViewBindings.findChildViewById(rootView, id);
      if (nativeAdCallToAction == null) {
        break missingId;
      }

      id = R.id.native_ad_icon;
      MediaView nativeAdIcon = ViewBindings.findChildViewById(rootView, id);
      if (nativeAdIcon == null) {
        break missingId;
      }

      id = R.id.native_ad_media;
      MediaView nativeAdMedia = ViewBindings.findChildViewById(rootView, id);
      if (nativeAdMedia == null) {
        break missingId;
      }

      id = R.id.native_ad_social_context;
      TextView nativeAdSocialContext = ViewBindings.findChildViewById(rootView, id);
      if (nativeAdSocialContext == null) {
        break missingId;
      }

      id = R.id.native_ad_sponsored_label;
      TextView nativeAdSponsoredLabel = ViewBindings.findChildViewById(rootView, id);
      if (nativeAdSponsoredLabel == null) {
        break missingId;
      }

      id = R.id.native_ad_title;
      TextView nativeAdTitle = ViewBindings.findChildViewById(rootView, id);
      if (nativeAdTitle == null) {
        break missingId;
      }

      return new NativeAdItemFacebookBinding((LinearLayout) rootView, adChoicesContainer,
          nativeAdBody, nativeAdCallToAction, nativeAdIcon, nativeAdMedia, nativeAdSocialContext,
          nativeAdSponsoredLabel, nativeAdTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
