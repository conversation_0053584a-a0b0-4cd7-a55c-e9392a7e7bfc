// Generated by view binder compiler. Do not edit!
package com.official.fivegfastvpn.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.official.fivegfastvpn.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import pl.droidsonroids.gif.GifImageView;

public final class ItemServersPremiumBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final TextView countryName;

  @NonNull
  public final ImageView flag;

  @NonNull
  public final GifImageView primeLg;

  private ItemServersPremiumBinding(@NonNull CardView rootView, @NonNull TextView countryName,
      @NonNull ImageView flag, @NonNull GifImageView primeLg) {
    this.rootView = rootView;
    this.countryName = countryName;
    this.flag = flag;
    this.primeLg = primeLg;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemServersPremiumBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemServersPremiumBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_servers_premium, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemServersPremiumBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.countryName;
      TextView countryName = ViewBindings.findChildViewById(rootView, id);
      if (countryName == null) {
        break missingId;
      }

      id = R.id.flag;
      ImageView flag = ViewBindings.findChildViewById(rootView, id);
      if (flag == null) {
        break missingId;
      }

      id = R.id.primeLg;
      GifImageView primeLg = ViewBindings.findChildViewById(rootView, id);
      if (primeLg == null) {
        break missingId;
      }

      return new ItemServersPremiumBinding((CardView) rootView, countryName, flag, primeLg);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
