<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">


    <androidx.cardview.widget.CardView
        android:id="@+id/cardOneMonth"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="10dp"
        app:cardCornerRadius="10dp"
        app:cardElevation="0dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="70dp">

            <pl.droidsonroids.gif.GifImageView
                android:id="@+id/primeLg"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="10dp"
                android:background="@drawable/upgrade" />


            <TextView
                android:id="@+id/item_pro_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="10dp"
                android:fontFamily="sans-serif"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:layout_toStartOf="@id/primeLg"
                android:layout_toEndOf="@id/item_pro_price"
                android:id="@+id/item_pro_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:gravity="center"
                android:padding="10dp"
                android:fontFamily="sans-serif"
                android:text="Monthly"
                android:textSize="12sp"
                android:textStyle="bold" />


        </RelativeLayout>


    </androidx.cardview.widget.CardView>

</RelativeLayout>