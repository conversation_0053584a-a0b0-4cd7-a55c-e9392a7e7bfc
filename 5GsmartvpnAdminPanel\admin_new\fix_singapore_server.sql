-- Fix Singapore Server Configuration
-- Replace the problematic Surfshark config with a working test configuration

UPDATE `servers` SET 
    `username` = 'testuser',
    `password` = 'testpass',
    `configFile` = 'client
dev tun
proto udp
remote 127.0.0.1 1194
resolv-retry infinite
nobind
persist-key
persist-tun
remote-cert-tls server
auth SHA256
cipher AES-256-CBC
verb 3
mute 20
auth-nocache
script-security 2
fast-io
comp-lzo no
pull
route-delay 2'
WHERE `id` = 2;

-- Add a simple test server that should work locally
INSERT INTO `servers` (`name`, `username`, `password`, `configFile`, `flagURL`, `type`, `pos`, `status`) VALUES
('Test Server', 'testuser', 'testpass', 
'client
dev tun
proto udp
remote ******* 1194
resolv-retry infinite
nobind
persist-key
persist-tun
remote-cert-tls server
auth SHA256
cipher AES-256-CBC
verb 3
mute 20
auth-nocache
script-security 2
fast-io
comp-lzo no
pull
route-delay 2', 
'test.png', 1, 1, 1);

-- Add USA server with public VPN configuration
INSERT INTO `servers` (`name`, `username`, `password`, `configFile`, `flagURL`, `type`, `pos`, `status`) VALUES
('United States', 'vpnuser', 'vpnpass', 
'client
dev tun
proto udp
remote us.vpnbook.com 1194
resolv-retry infinite
nobind
persist-key
persist-tun
remote-cert-tls server
auth SHA256
cipher AES-256-CBC
verb 3
mute 20
auth-nocache
script-security 2
fast-io
comp-lzo no
pull
route-delay 2', 
'usa.png', 1, 2, 1);

-- Add UK server
INSERT INTO `servers` (`name`, `username`, `password`, `configFile`, `flagURL`, `type`, `pos`, `status`) VALUES
('United Kingdom', 'vpnuser', 'vpnpass', 
'client
dev tun
proto udp
remote uk.vpnbook.com 1194
resolv-retry infinite
nobind
persist-key
persist-tun
remote-cert-tls server
auth SHA256
cipher AES-256-CBC
verb 3
mute 20
auth-nocache
script-security 2
fast-io
comp-lzo no
pull
route-delay 2', 
'uk.png', 1, 3, 1);
