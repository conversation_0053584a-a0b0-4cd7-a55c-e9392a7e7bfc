// Generated by view binder compiler. Do not edit!
package com.official.fivegfastvpn.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RatingBar;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.gms.ads.nativead.MediaView;
import com.google.android.gms.ads.nativead.NativeAdView;
import com.official.fivegfastvpn.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class NativeAdsItemAdmobBinding implements ViewBinding {
  @NonNull
  private final NativeAdView rootView;

  @NonNull
  public final TextView adAdvertiser;

  @NonNull
  public final TextView adBody;

  @NonNull
  public final Button adCallToAction;

  @NonNull
  public final TextView adHeadline;

  @NonNull
  public final ImageView adIcon;

  @NonNull
  public final MediaView adMedia;

  @NonNull
  public final TextView adPrice;

  @NonNull
  public final RatingBar adStars;

  @NonNull
  public final TextView adStore;

  @NonNull
  public final CardView cvVideoThumb;

  @NonNull
  public final LinearLayout llbottom;

  @NonNull
  public final NativeAdView llparent;

  @NonNull
  public final LinearLayout llprice;

  @NonNull
  public final RelativeLayout profile;

  @NonNull
  public final ImageView removeadd;

  @NonNull
  public final RelativeLayout rlInstall;

  @NonNull
  public final RelativeLayout rladView;

  @NonNull
  public final RelativeLayout rladtext;

  private NativeAdsItemAdmobBinding(@NonNull NativeAdView rootView, @NonNull TextView adAdvertiser,
      @NonNull TextView adBody, @NonNull Button adCallToAction, @NonNull TextView adHeadline,
      @NonNull ImageView adIcon, @NonNull MediaView adMedia, @NonNull TextView adPrice,
      @NonNull RatingBar adStars, @NonNull TextView adStore, @NonNull CardView cvVideoThumb,
      @NonNull LinearLayout llbottom, @NonNull NativeAdView llparent, @NonNull LinearLayout llprice,
      @NonNull RelativeLayout profile, @NonNull ImageView removeadd,
      @NonNull RelativeLayout rlInstall, @NonNull RelativeLayout rladView,
      @NonNull RelativeLayout rladtext) {
    this.rootView = rootView;
    this.adAdvertiser = adAdvertiser;
    this.adBody = adBody;
    this.adCallToAction = adCallToAction;
    this.adHeadline = adHeadline;
    this.adIcon = adIcon;
    this.adMedia = adMedia;
    this.adPrice = adPrice;
    this.adStars = adStars;
    this.adStore = adStore;
    this.cvVideoThumb = cvVideoThumb;
    this.llbottom = llbottom;
    this.llparent = llparent;
    this.llprice = llprice;
    this.profile = profile;
    this.removeadd = removeadd;
    this.rlInstall = rlInstall;
    this.rladView = rladView;
    this.rladtext = rladtext;
  }

  @Override
  @NonNull
  public NativeAdView getRoot() {
    return rootView;
  }

  @NonNull
  public static NativeAdsItemAdmobBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static NativeAdsItemAdmobBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.native_ads_item_admob, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static NativeAdsItemAdmobBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ad_advertiser;
      TextView adAdvertiser = ViewBindings.findChildViewById(rootView, id);
      if (adAdvertiser == null) {
        break missingId;
      }

      id = R.id.ad_body;
      TextView adBody = ViewBindings.findChildViewById(rootView, id);
      if (adBody == null) {
        break missingId;
      }

      id = R.id.ad_call_to_action;
      Button adCallToAction = ViewBindings.findChildViewById(rootView, id);
      if (adCallToAction == null) {
        break missingId;
      }

      id = R.id.ad_headline;
      TextView adHeadline = ViewBindings.findChildViewById(rootView, id);
      if (adHeadline == null) {
        break missingId;
      }

      id = R.id.ad_icon;
      ImageView adIcon = ViewBindings.findChildViewById(rootView, id);
      if (adIcon == null) {
        break missingId;
      }

      id = R.id.ad_media;
      MediaView adMedia = ViewBindings.findChildViewById(rootView, id);
      if (adMedia == null) {
        break missingId;
      }

      id = R.id.ad_price;
      TextView adPrice = ViewBindings.findChildViewById(rootView, id);
      if (adPrice == null) {
        break missingId;
      }

      id = R.id.ad_stars;
      RatingBar adStars = ViewBindings.findChildViewById(rootView, id);
      if (adStars == null) {
        break missingId;
      }

      id = R.id.ad_store;
      TextView adStore = ViewBindings.findChildViewById(rootView, id);
      if (adStore == null) {
        break missingId;
      }

      id = R.id.cv_video_thumb;
      CardView cvVideoThumb = ViewBindings.findChildViewById(rootView, id);
      if (cvVideoThumb == null) {
        break missingId;
      }

      id = R.id.llbottom;
      LinearLayout llbottom = ViewBindings.findChildViewById(rootView, id);
      if (llbottom == null) {
        break missingId;
      }

      NativeAdView llparent = (NativeAdView) rootView;

      id = R.id.llprice;
      LinearLayout llprice = ViewBindings.findChildViewById(rootView, id);
      if (llprice == null) {
        break missingId;
      }

      id = R.id.profile;
      RelativeLayout profile = ViewBindings.findChildViewById(rootView, id);
      if (profile == null) {
        break missingId;
      }

      id = R.id.removeadd;
      ImageView removeadd = ViewBindings.findChildViewById(rootView, id);
      if (removeadd == null) {
        break missingId;
      }

      id = R.id.rl_install;
      RelativeLayout rlInstall = ViewBindings.findChildViewById(rootView, id);
      if (rlInstall == null) {
        break missingId;
      }

      id = R.id.rladView;
      RelativeLayout rladView = ViewBindings.findChildViewById(rootView, id);
      if (rladView == null) {
        break missingId;
      }

      id = R.id.rladtext;
      RelativeLayout rladtext = ViewBindings.findChildViewById(rootView, id);
      if (rladtext == null) {
        break missingId;
      }

      return new NativeAdsItemAdmobBinding((NativeAdView) rootView, adAdvertiser, adBody,
          adCallToAction, adHeadline, adIcon, adMedia, adPrice, adStars, adStore, cvVideoThumb,
          llbottom, llparent, llprice, profile, removeadd, rlInstall, rladView, rladtext);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
