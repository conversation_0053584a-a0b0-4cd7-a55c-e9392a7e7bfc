// Generated by view binder compiler. Do not edit!
package com.official.fivegfastvpn.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.official.fivegfastvpn.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import pl.droidsonroids.gif.GifImageView;

public final class ItemPremiumBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final CardView cardOneMonth;

  @NonNull
  public final TextView itemProPrice;

  @NonNull
  public final TextView itemProTime;

  @NonNull
  public final GifImageView primeLg;

  private ItemPremiumBinding(@NonNull RelativeLayout rootView, @NonNull CardView cardOneMonth,
      @NonNull TextView itemProPrice, @NonNull TextView itemProTime,
      @NonNull GifImageView primeLg) {
    this.rootView = rootView;
    this.cardOneMonth = cardOneMonth;
    this.itemProPrice = itemProPrice;
    this.itemProTime = itemProTime;
    this.primeLg = primeLg;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemPremiumBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemPremiumBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_premium, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemPremiumBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cardOneMonth;
      CardView cardOneMonth = ViewBindings.findChildViewById(rootView, id);
      if (cardOneMonth == null) {
        break missingId;
      }

      id = R.id.item_pro_price;
      TextView itemProPrice = ViewBindings.findChildViewById(rootView, id);
      if (itemProPrice == null) {
        break missingId;
      }

      id = R.id.item_pro_time;
      TextView itemProTime = ViewBindings.findChildViewById(rootView, id);
      if (itemProTime == null) {
        break missingId;
      }

      id = R.id.primeLg;
      GifImageView primeLg = ViewBindings.findChildViewById(rootView, id);
      if (primeLg == null) {
        break missingId;
      }

      return new ItemPremiumBinding((RelativeLayout) rootView, cardOneMonth, itemProPrice,
          itemProTime, primeLg);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
