<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/FF5722"
    tools:context=".activity.About_Us">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar" />

    <ImageView
        android:id="@+id/image"
        android:layout_width="150dp"
        android:layout_height="150dp"
        android:src="@mipmap/ic_launcher_round"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="100dp"/>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/image"
        android:layout_above="@id/version"
        >

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp">



            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/about"
                android:textSize="30dp"
                android:textStyle="normal"
                android:textAlignment="textStart"
                android:fontFamily="@font/radiocan"
                android:textColor="@color/white"
                android:layout_centerHorizontal="true"
                android:layout_below="@id/app_text"
                android:id="@+id/app_text"
                />


        </RelativeLayout>
    </ScrollView>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Version"
        android:textColor="@color/white"
        android:layout_marginTop="50dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:textStyle="bold"
        android:layout_marginBottom="20dp"
        android:id="@+id/version"
        android:textSize="18dp"/>
</RelativeLayout>
